package org.example.demostudy.service;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.example.demostudy.Mapper.AdminMapper;
import org.example.demostudy.entity.Account;
import org.example.demostudy.entity.Admin;
import org.example.demostudy.entity.Admin;
import org.example.demostudy.exception.CustomException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional
@Service
public class AdminService {

    @Resource
    private AdminMapper adminMapper;

    public void add(Admin admin) {
        // 获取要新增管理员的账号
        String username = admin.getUsername();
        // 根据账号查询数据库中是否已存在该管理员
        Admin dbAdmin = adminMapper.selectByUsername(username);
        // 若已存在，抛出自定义异常阻止新增
        if (dbAdmin != null) {
            throw new CustomException("500", "账号已存在，请更换别的账号");
        }
        // 若密码为空，设置默认密码 "123"
        if (StrUtil.isBlank(admin.getPassword())) {
            admin.setPassword("admin");
        }
        // 若姓名为空，将账号设为默认姓名
        if (StrUtil.isBlank(admin.getName())) {
            admin.setName(admin.getUsername());
        }
        // 固定设置角色为 "ADMIN"（管理员角色）
        admin.setRole("ADMIN");
        // 执行新增操作，将管理员信息插入数据库
        adminMapper.insert(admin);
    }

    public void update(Admin admin) {
        adminMapper.updateById(admin);
    }

    public void deleteById(Integer id) {
        adminMapper.deleteById(id);
    }

    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids)
            this.deleteById(id);
    }

    public Admin selectById(Integer id) {
        return adminMapper.selectById(id);
    }

    public List<Admin> selectList(Admin admin) {
        return adminMapper.selectAll(admin);
    }

    public PageInfo<Admin> selectPage(Admin admin, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Admin> list = adminMapper.selectAll(admin);
        return PageInfo.of(list);
    }
    public Admin login(Account account) {
        String username = account.getUsername();  // 账号
        Admin dbAdmin = adminMapper.selectByUsername(username);
        if (dbAdmin == null) {  // 没查询到任何用户  说明没有这个账号
            throw new CustomException("500", "账号不存在");
        }
        // 数据库存在这个账号
        String password = account.getPassword();
        if (!dbAdmin.getPassword().equals(password)) {  // 用户输入的密码跟数据库账号的密码不匹配
            throw new CustomException("500", "账号或密码错误");
        }
        return dbAdmin;
    }
}
