package org.example.demostudy.controller;
import jakarta.annotation.Resource;
import org.example.demostudy.common.Result;
import org.example.demostudy.entity.Account;
import org.example.demostudy.entity.Employee;
import org.example.demostudy.exception.CustomException;
import org.example.demostudy.service.AdminService;
import org.example.demostudy.service.EmployeeService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;


@RestController
public class webcontroller {

    @Resource
    private EmployeeService employeeService;

    @Resource
    private AdminService adminService;

    @GetMapping("/hello")
    public Result sayhello() {
        return Result.success("hello world");
    }

    @PostMapping("/login")
    public Result login(@RequestBody Account account) {
        Account result = null;
        if ("ADMIN".equals(account.getRole())) {  // 管理员登录
            result = adminService.login(account);
        } else if ("EMP".equals(account.getRole())) {
            result = employeeService.login(account);
        }
        return Result.success(result);
    }

    @PostMapping("/register")
    public Result register(@RequestBody Employee employee) {
        employeeService.register(employee);
        return Result.success();
    }
}


