package org.example.demostudy.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;

/**
 * OpenAPI配置类
 * 简化配置，避免版本兼容性问题
 */
@Configuration
class OpenApiConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("员工管理系统API")
                        .description("员工管理系统后端接口文档")
                        .version("1.0"));
    }
}
