<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.demostudy.Mapper.AdminMapper">

    <select id="selectAll" resultType="org.example.demostudy.entity.Admin">
        select * from admin
        <where>
            <if test="name != null">name like concat('%', #{name}, '%')</if>
        </where>
        order by id desc
    </select>

    <insert id="insert" parameterType="org.example.demostudy.entity.Admin">
        insert into `admin` (id , username , password , role , name)
        values (#{id} , #{username}, #{password}, #{role}, #{name})
    </insert>

    <update id="updateById" parameterType="org.example.demostudy.entity.Admin">
        update `admin` set username = #{username}, password = #{password}, role = #{role}, name = #{name}
        where id = #{id}
    </update>
</mapper>