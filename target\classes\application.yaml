server:
  port: 9090


spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456
    url: **********************************************************************************************************

# 配置mybatis实体和xml映射
mybatis:
  ## 映射xml
  mapper-locations: classpath:mapper/*.xml
  configuration:
    # 配置日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true

# Knife4j配置
knife4j:
  # 是否启用Knife4j增强模式
  enable: true
  # 是否启用生产环境屏蔽
  production: false
  # 基本信息配置
  basic:
    # 是否启用登录认证
    enable: false
    # 用户名
    username: admin
    # 密码
    password: admin123
  # 设置
  setting:
    # 是否启用动态参数调试功能
    enable-dynamic-parameter: true
    # 是否显示Footer
    enable-footer: true
    # 是否启用主页自定义
    enable-home-custom: true
    # 主页内容
    home-custom-location: classpath:markdown/home.md
    # 是否启用搜索
    enable-search: true
    # 是否启用缓存
    enable-cache: true
    # 是否启用过滤多余的响应状态码
    enable-filter-multipart-api-method-type: true

# SpringDoc配置 (OpenAPI 3.0)
springdoc:
  # API文档配置
  api-docs:
    # 是否启用API文档
    enabled: true
    # API文档路径
    path: /v3/api-docs
  # Swagger UI配置
  swagger-ui:
    # 是否启用Swagger UI
    enabled: true
    # Swagger UI路径
    path: /swagger-ui.html
    # 操作排序
    operations-sorter: alpha
    # 标签排序
    tags-sorter: alpha
    # 是否启用深度链接
    deep-linking: true
    # 是否显示扩展
    display-extensions: true
    # 默认模型展开深度
    default-models-expand-depth: 1
    # 默认模型渲染
    default-model-rendering: example
    # 是否显示请求持续时间
    display-request-duration: true
    # 文档展开设置
    doc-expansion: none
    # 过滤器
    filter: false
    # 最大显示标签数
    max-displayed-tags: 10
    # 是否显示通用扩展
    show-extensions: true
    # 是否显示通用扩展
    show-common-extensions: true
  # 分组配置
  group-configs:
    - group: 'admin'
      display-name: '管理员接口'
      paths-to-match: '/admin/**'
    - group: 'employee'
      display-name: '员工接口'
      paths-to-match: '/employee/**'
    - group: 'files'
      display-name: '文件接口'
      paths-to-match: '/files/**'
    - group: 'common'
      display-name: '通用接口'
      paths-to-match: '/**'
      paths-to-exclude: '/admin/**,/employee/**,/files/**'