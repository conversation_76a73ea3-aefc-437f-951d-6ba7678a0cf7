# Knife4j API文档配置说明

## 1. 已完成的配置

### 1.1 Maven依赖配置
已在 `pom.xml` 中添加了以下依赖：

```xml
<!-- Knife4j Spring Boot 3 Starter -->
<dependency>
    <groupId>com.github.xiaoymin</groupId>
    <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
    <version>4.4.0</version>
</dependency>

<!-- SpringDoc OpenAPI 3 -->
<dependency>
    <groupId>org.springdoc</groupId>
    <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    <version>2.2.0</version>
</dependency>
```

### 1.2 application.yaml配置
已在配置文件中添加了完整的Knife4j配置：

```yaml
# Knife4j配置
knife4j:
  enable: true                    # 启用Knife4j增强模式
  production: false              # 非生产环境
  basic:
    enable: false                 # 是否启用登录认证
    username: admin              # 登录用户名
    password: admin123           # 登录密码
  setting:
    enable-dynamic-parameter: true      # 启用动态参数调试
    enable-footer: true                 # 显示Footer
    enable-home-custom: true           # 启用主页自定义
    enable-search: true                # 启用搜索
    enable-cache: true                 # 启用缓存

# SpringDoc配置
springdoc:
  api-docs:
    enabled: true                # 启用API文档
    path: /v3/api-docs          # API文档路径
  swagger-ui:
    enabled: true                # 启用Swagger UI
    path: /swagger-ui.html       # Swagger UI路径
    operations-sorter: alpha     # 操作排序
    tags-sorter: alpha          # 标签排序
  group-configs:                 # 分组配置
    - group: 'admin'
      display-name: '管理员接口'
      paths-to-match: '/admin/**'
    - group: 'employee'
      display-name: '员工接口'
      paths-to-match: '/employee/**'
    - group: 'files'
      display-name: '文件接口'
      paths-to-match: '/files/**'
    - group: 'common'
      display-name: '通用接口'
      paths-to-match: '/**'
      paths-to-exclude: '/admin/**,/employee/**,/files/**'
```

### 1.3 Swagger配置类
已创建 `SwaggerConfig.java` 配置基本信息：

```java
@Configuration
public class SwaggerConfig {
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("员工管理系统API文档")
                        .description("基于Spring Boot 3 + MyBatis的员工管理系统后端API接口文档")
                        .version("1.0.0"));
    }
}
```

## 2. 需要执行的步骤

### 2.1 重新加载Maven依赖
1. 在IDE中右键点击项目
2. 选择 "Maven" -> "Reload project" 或 "Refresh"
3. 等待依赖下载完成

### 2.2 启动项目
启动Spring Boot应用后，可以通过以下地址访问API文档：

- **Knife4j增强文档**: http://localhost:9090/doc.html
- **原生Swagger UI**: http://localhost:9090/swagger-ui.html
- **OpenAPI JSON**: http://localhost:9090/v3/api-docs

## 3. 为Controller添加Swagger注解（可选）

### 3.1 导入注解
```java
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
```

### 3.2 添加注解示例
```java
@Tag(name = "通用接口", description = "系统通用功能接口")
@RestController
public class webcontroller {

    @Operation(summary = "用户登录", description = "支持管理员和员工登录")
    @PostMapping("/login")
    public Result login(
            @Parameter(description = "登录账户信息") 
            @RequestBody Account account) {
        // 方法实现
    }
}
```

## 4. 常用注解说明

- `@Tag`: 为Controller添加标签和描述
- `@Operation`: 为接口方法添加说明
- `@Parameter`: 为参数添加描述
- `@Schema`: 为实体类字段添加说明
- `@ApiResponse`: 定义响应信息

## 5. 配置说明

### 5.1 安全配置
如需启用访问认证，修改配置：
```yaml
knife4j:
  basic:
    enable: true
    username: your_username
    password: your_password
```

### 5.2 生产环境配置
生产环境建议关闭文档：
```yaml
knife4j:
  production: true  # 生产环境屏蔽文档
```

## 6. 访问地址总结

配置完成后，可通过以下地址访问：

1. **Knife4j文档**: http://localhost:9090/doc.html （推荐）
2. **Swagger UI**: http://localhost:9090/swagger-ui.html
3. **API JSON**: http://localhost:9090/v3/api-docs

Knife4j提供了更美观的界面和更多功能，建议优先使用。
