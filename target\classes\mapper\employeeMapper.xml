<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.example.demostudy.Mapper.EmployeeMapper">

    <select id="selectAll" resultType="org.example.demostudy.entity.Employee">
        select * from employee
        <where>
            <if test="name != null">name like concat('%', #{name}, '%')</if>
        </where>
        order by id desc
    </select>

    <insert id="insert" parameterType="org.example.demostudy.entity.Employee">
        insert into `employee` (id , username , password , role , name , sex , no , age , description , depatment_id)
        values (#{id} , #{username}, #{password}, #{role}, #{name} , #{sex} , #{no} , #{age} , #{description} , #{departmentId})
    </insert>

    <update id="updateById" parameterType="org.example.demostudy.entity.Employee">
        update `employee` set username = #{username}, password = #{password}, role = #{role}, name = #{name}, sex = #{sex}, no = #{no}, age = #{age},
                              description = #{description}, depatment_id = #{departmentId}
        where id = #{id}
    </update>
</mapper>