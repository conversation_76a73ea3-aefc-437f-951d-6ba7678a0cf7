package org.example.demostudy.controller;

import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.apache.ibatis.annotations.Delete;
import org.example.demostudy.common.Result;
import org.example.demostudy.entity.Employee;
import org.example.demostudy.service.EmployeeService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/employee")
public class EmployeeController {

    @Resource
    private EmployeeService employeeService;
//    查询所有的数据

    /**
     * 新增数据
     */
    @PostMapping("/add")
    public Result add(@RequestBody Employee employee) {
        employeeService.add(employee);
        return Result.success();
    }

    @PutMapping("/update")
    public Result update(@RequestBody Employee employee) {
        employeeService.update(employee);
        return Result.success();
    }

    @DeleteMapping("/deletById/{id}")
    public Result deletById(@PathVariable Integer id) {
        employeeService.deletById(id);
        return Result.success();
    }
    @DeleteMapping("/deletBatch")
    public Result deletBatch(@RequestBody List< Integer> ids) {
        employeeService.deletBatch(ids);
        return Result.success();
    }


    @GetMapping("/selectAll")
    public Result selectAll(Employee employee) {
        List<Employee> list = employeeService.selectAll(employee);
        return Result.success(list);
    }

    @GetMapping("/selectById/{id}/{no}")
    public Result selectById(@PathVariable Integer id, @PathVariable String no) {
        Employee employee = employeeService.selectById(id);
        return Result.success(employee);
    }

    @GetMapping("/selectOne")
    public Result selectOne(@RequestParam Integer id, @RequestParam(required = false) String no) {
        Employee employee = employeeService.selectById(id);
        return Result.success(employee);
    }

    @GetMapping("/selectList")
    public Result selectList(Employee employee) {
        List<Employee> list = employeeService.selectList(employee);
        return Result.success(list);
    }

    /**
     * 分页查询的数据
     * pageNum: 当前页码
     * pageSize: 每页的个数
     */
    @GetMapping("/selectPage")
    public Result selectPage(Employee employee,
                             @RequestParam(defaultValue = "1") Integer pageNum,
                             @RequestParam(defaultValue = "5") Integer pageSize) {
        PageInfo<Employee> PageInfo = employeeService.selectPage(employee, pageNum, pageSize);
        return Result.success(PageInfo);
    }
}
