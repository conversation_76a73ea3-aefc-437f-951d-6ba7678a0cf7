package org.example.demostudy.service;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.example.demostudy.Mapper.EmployeeMapper;
import org.example.demostudy.entity.Account;
import org.example.demostudy.entity.Admin;
import org.example.demostudy.entity.Employee;
import org.example.demostudy.exception.CustomException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional
@Service
public class EmployeeService {

    @Resource
    private EmployeeMapper employeeMapper;
    public List<Employee> selectAll(Employee employee) {
        return employeeMapper.selectAll(employee);
    }

//    public void add(Employee employee) {
//        employeeMapper.insert(employee);
//    }
    public void add(Employee employee) {
        // 获取要新增员工的账号
        String username = employee.getUsername();
        // 根据账号查询数据库中是否已存在该员工
        Employee dbEmployee = employeeMapper.selectByUsername(username);
        // 若已存在，抛出自定义异常阻止新增
        if (dbEmployee != null) {
            throw new CustomException("500", "账号已存在，请更换别的账号");
        }
        // 若密码为空，设置默认密码 "123"
        if (StrUtil.isBlank(employee.getPassword())) {
            employee.setPassword("123");
        }
        // 若姓名为空，将账号设为默认姓名
        if (StrUtil.isBlank(employee.getName())) {
            employee.setName(employee.getUsername());
        }
        // 固定设置角色为 "EMP"（员工角色 ）
        employee.setRole("EMP");
        // 执行新增操作，将员工信息插入数据库
        employeeMapper.insert(employee);
    }
    public void update(Employee employee) {
        employeeMapper.updateById(employee);
    }


    public void deletById(Integer id) {
        employeeMapper.deletById(id);
    }

    public void deletBatch(List<Integer> ids) {
        for (Integer id : ids)
            this.deletById(id);
    }

    public Employee selectById(Integer id) {
        return employeeMapper.selectById(id);
    }

    public List<Employee> selectList(Employee employee) {
        System.out.println( employee);
        return null;
    }

    public void register(Employee employee) {
        String username = employee.getUsername();
        Employee dbEmployee = employeeMapper.selectByUsername(username);
        if (dbEmployee != null) {
            throw new CustomException("500", "账号已存在，请更换别的账号");
        }
        // 若密码为空，设置默认密码 "123"
        if (StrUtil.isBlank(employee.getPassword())) {
            employee.setPassword("123");
        }
        // 若姓名为空，将账号设为默认姓名
        if (StrUtil.isBlank(employee.getName())) {
            employee.setName(employee.getUsername());
        }
        // 固定设置角色为 "EMP"（员工角色 ）
        employee.setRole("EMP");
        // 执行新增操作，将员工信息插入数据库
        employeeMapper.insert(employee);
    }

    public PageInfo<Employee> selectPage(Employee employee, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Employee> list = employeeMapper.selectAll(employee);
        return PageInfo.of(list);
    }


    public Employee login(Account account) {
        String username = account.getUsername();  // 账号
        Employee dbEmployee = employeeMapper.selectByUsername(username);
        if (dbEmployee == null) {  // 没查询到任何用户  说明没有这个账号
            throw new CustomException("500", "账号不存在");
        }
        // 数据库存在这个账号
        String password = account.getPassword();
        if (!dbEmployee.getPassword().equals(password)) {  // 用户输入的密码跟数据库账号的密码不匹配
            throw new CustomException("500", "账号或密码错误");
        }
        return dbEmployee;
    }
}


