package org.example.demostudy.controller;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.example.demostudy.common.Result;
import org.example.demostudy.entity.Admin;
import org.example.demostudy.service.AdminService;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/admin")
public class AdminController {

    @Resource
    private AdminService adminService;
//    查询所有的数据

    /**
     * 新增数据
     */
    @PostMapping("/add")
    public Result add(@RequestBody Admin admin) {
        adminService.add(admin);
        return Result.success();
    }

    @PutMapping("/update")
    public Result update(@RequestBody Admin admin) {
        adminService.update(admin);
        return Result.success();
    }

    @DeleteMapping("/deletById/{id}")
    public Result deletById(@PathVariable Integer id) {
        adminService.deleteById(id);
        return Result.success();
    }
    @DeleteMapping("/deletBatch")
    public Result deletBatch(@RequestBody List< Integer> ids) {
        adminService.deleteBatch(ids);
        return Result.success();
    }




    @GetMapping("/selectById/{id}/{no}")
    public Result selectById(@PathVariable Integer id, @PathVariable String no) {
        Admin admin = adminService.selectById(id);
        return Result.success(admin);
    }

    @GetMapping("/selectOne")
    public Result selectOne(@RequestParam Integer id, @RequestParam(required = false) String no) {
        Admin admin = adminService.selectById(id);
        return Result.success(admin);
    }

    @GetMapping("/selectList")
    public Result selectList(Admin admin) {
        List<Admin> list = adminService.selectList(admin);
        return Result.success(list);
    }

    /**
     * 分页查询的数据
     * pageNum: 当前页码
     * pageSize: 每页的个数
     */
    @GetMapping("/selectPage")
    public Result selectPage(Admin admin,
                             @RequestParam(defaultValue = "1") Integer pageNum,
                             @RequestParam(defaultValue = "5") Integer pageSize) {
        PageInfo<Admin> PageInfo = adminService.selectPage(admin, pageNum, pageSize);
        return Result.success(PageInfo);
    }
}
