<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="BeansEndpointTabSettings">
    <option name="showContexts" value="false" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="327dc55b-55bd-4327-9a31-cec8f530d95f" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/src/main/resources/mapper/employeeMapper.xml" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="F:\maven\apache-maven-3.9.10" />
        <option name="localRepository" value="F:\maven\apache-maven-3.9.10\repo" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="F:\maven\apache-maven-3.9.10\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="21" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="3054L8ieotIFKLOigtzsKxC8iBX" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.DemostudyApplication.executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.DemostudyApplication.executor&quot;: &quot;Run&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/activity&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.DemostudyApplication">
    <configuration name="DemostudyApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="org.example.demostudy.DemostudyApplication" />
      <module name="demostudy" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.example.demostudy.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DemostudyApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="demostudy" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.example.demostudy.DemostudyApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.DemostudyApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.15989.150" />
        <option value="bundled-js-predefined-1d06a55b98c1-91d5c284f522-JavaScript-IU-241.15989.150" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="327dc55b-55bd-4327-9a31-cec8f530d95f" name="Changes" comment="" />
      <created>1752904041870</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752904041870</updated>
      <workItem from="1752904042973" duration="1540000" />
      <workItem from="1752905596950" duration="1827000" />
      <workItem from="1752977919133" duration="19132000" />
      <workItem from="1753056727393" duration="15123000" />
      <workItem from="1753110148393" duration="206000" />
      <workItem from="1753110801776" duration="435000" />
      <workItem from="1753150827490" duration="16175000" />
      <workItem from="1753230233368" duration="4815000" />
      <workItem from="1753276683081" duration="6504000" />
      <workItem from="1753324025732" duration="10308000" />
      <workItem from="1753364340841" duration="33000" />
      <workItem from="1753407781212" duration="107000" />
      <workItem from="1753428112215" duration="62000" />
      <workItem from="1753439946219" duration="7000" />
      <workItem from="1753440160404" duration="26000" />
      <workItem from="1753440367432" duration="17000" />
      <workItem from="1753440403959" duration="1000" />
      <workItem from="1753440482303" duration="7000" />
      <workItem from="1753440514147" duration="11000" />
      <workItem from="1753440624516" duration="13000" />
      <workItem from="1753441265311" duration="16000" />
      <workItem from="1753441818587" duration="38000" />
      <workItem from="1753441896115" duration="3000" />
      <workItem from="1753441910056" duration="7000" />
      <workItem from="1753441975826" duration="14000" />
      <workItem from="1753443670217" duration="5000" />
      <workItem from="1753443715215" duration="13000" />
      <workItem from="1753444091399" duration="56000" />
      <workItem from="1753444166638" duration="33000" />
      <workItem from="1753444913873" duration="2000" />
      <workItem from="1753449527852" duration="2000" />
      <workItem from="1753452549420" duration="1000" />
      <workItem from="1753455943631" duration="8000" />
      <workItem from="1753505627801" duration="27000" />
      <workItem from="1753505759732" duration="22000" />
      <workItem from="1753505801062" duration="4000" />
      <workItem from="1753533446312" duration="2000" />
      <workItem from="1753533695342" duration="1000" />
      <workItem from="1753671960181" duration="10069000" />
      <workItem from="1753767607569" duration="13770000" />
      <workItem from="1753841027058" duration="7279000" />
      <workItem from="1753880041267" duration="2000" />
      <workItem from="1753889940890" duration="2000" />
      <workItem from="1753890235350" duration="1000" />
      <workItem from="1754018136005" duration="9000" />
      <workItem from="1754044988467" duration="746000" />
      <workItem from="1754130668262" duration="302000" />
      <workItem from="1754140357637" duration="46000" />
      <workItem from="1754293373697" duration="1917000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>