D:\spring-boot3\demostudy\src\main\java\org\example\demostudy\common\Result.java
D:\spring-boot3\demostudy\src\main\java\org\example\demostudy\controller\EmployeeController.java
D:\spring-boot3\demostudy\src\main\java\org\example\demostudy\controller\webcontroller.java
D:\spring-boot3\demostudy\src\main\java\org\example\demostudy\DemostudyApplication.java
D:\spring-boot3\demostudy\src\main\java\org\example\demostudy\entity\Employee.java
D:\spring-boot3\demostudy\src\main\java\org\example\demostudy\exception\CustomException.java
D:\spring-boot3\demostudy\src\main\java\org\example\demostudy\exception\GlobalExceptionHandler.java
D:\spring-boot3\demostudy\src\main\java\org\example\demostudy\Mapper\EmployeeMapper.java
D:\spring-boot3\demostudy\src\main\java\org\example\demostudy\service\EmployeeService.java
